<?php

declare(strict_types=1);

/**
 * Test the new structure with autoloading
 * 
 * Usage: php tools/structure_validation.php
 */

// Load the bootstrap file which handles autoloading (adjust path for tools directory)
require_once __DIR__ . '/../tests/bootstrap.php';

use QrCodeGen\Core\QrCode;
use QrCodeGen\Core\QrSegment;
use QrCodeGen\Core\Enums\Ecc;
use QrCodeGen\Core\Enums\Mode;

echo "Testing New Structure with Autoloading\n";
echo "=====================================\n\n";

try {
    // Test Ecc enum from new location
    echo "Testing Core\\Enums\\Ecc:\n";
    echo "LOW ordinal: " . Ecc::LOW->getOrdinal() . " (expected: 0)\n";
    echo "MEDIUM ordinal: " . Ecc::MEDIUM->getOrdinal() . " (expected: 1)\n\n";

    // Test Mode enum from new location
    echo "Testing Core\\Enums\\Mode:\n";
    echo "NUMERIC mode bits: " . Mode::NUMERIC->getModeBits() . " (expected: 1)\n";
    echo "BYTE mode bits: " . Mode::BYTE->getModeBits() . " (expected: 4)\n\n";

    // Test QrSegment from new location
    echo "Testing Core\\QrSegment:\n";
    $segments = QrSegment::makeSegments("Hello, World!");
    echo "Segments created: " . count($segments) . " (expected: 1)\n";
    echo "First segment mode: " . $segments[0]->mode->name . " (expected: BYTE)\n\n";

    // Test QrCode from new location
    echo "Testing Core\\QrCode:\n";
    $qr = QrCode::encodeText("Hello, World!", Ecc::MEDIUM);
    echo "QR Code version: " . $qr->version . " (expected: positive integer)\n";
    echo "QR Code size: " . $qr->size . " (expected: positive integer)\n";
    echo "QR Code error correction: " . $qr->errorCorrectionLevel->name . " (expected: MEDIUM)\n\n";

    // Test utility functions from new location
    echo "Testing Core\\Utilities functions:\n";
    $bb = [];
    \QrCodeGen\Core\Utilities\appendBits(7, 3, $bb); // 7 = 111 in binary
    echo "appendBits(7, 3): [" . implode(', ', $bb) . "] (expected: [1, 1, 1])\n";
    
    $bit0 = \QrCodeGen\Core\Utilities\getBit(7, 0);
    $bit1 = \QrCodeGen\Core\Utilities\getBit(7, 1);
    $bit2 = \QrCodeGen\Core\Utilities\getBit(7, 2);
    echo "getBit(7, 0): " . ($bit0 ? 'true' : 'false') . " (expected: true)\n";
    echo "getBit(7, 1): " . ($bit1 ? 'true' : 'false') . " (expected: true)\n";
    echo "getBit(7, 2): " . ($bit2 ? 'true' : 'false') . " (expected: true)\n\n";

    echo "✅ All new structure tests passed!\n";
    echo "✅ New autoloading is working correctly!\n";
    echo "✅ Core classes are accessible in their new locations!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "❌ File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}
