<?php

declare(strict_types=1);

/**
 * QR Code Logo Embedding Examples.
 *
 * This file demonstrates various ways to embed logos in QR codes
 * using the QrCodeLogo functionality.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use QrCodeGen\Ecc;
use QrCodeGen\Exceptions\LogoException;
use QrCodeGen\LogoConfig;
use QrCodeGen\QrCode;
use QrCodeGen\QrCodeLogo;

echo "QR Code Logo Embedding Examples\n";
echo "================================\n\n";

// Example 1: Basic logo embedding with SVG string
echo "Example 1: Basic Logo Embedding\n";
echo "-------------------------------\n";

try {
    // Create a simple SVG logo as a string
    $logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
        <circle cx="50" cy="50" r="40" fill="#007bff"/>
        <text x="50" y="55" text-anchor="middle" fill="white" font-family="Arial" font-size="20" font-weight="bold">QR</text>
    </svg>';

    // Generate QR code
    $qr = QrCode::encodeText('https://example.com/logo-demo', Ecc::MEDIUM);

    // Create logo configuration
    $logoConfig = new LogoConfig(
        logoSource: $logoSvg,
        sizePercentage: 0.15, // 15% of QR code size
        addBackground: true,
        backgroundColor: '#FFFFFF'
    );

    // Create QR code with logo
    $qrWithLogo = new QrCodeLogo($qr, $logoConfig);

    // Generate SVG output
    $svgOutput = $qrWithLogo->toSvg(border: 4);
    file_put_contents(__DIR__ . '/output_logo_basic.svg', $svgOutput);

    echo "✓ Generated: output_logo_basic.svg\n";
    echo "  QR Code: {$qr->size}×{$qr->size} modules\n";
    echo "  Error Correction: {$qr->errorCorrectionLevel->name}\n";
    echo "  Logo Size: 15% of QR code\n\n";
} catch (LogoException $e) {
    echo "✗ Error: {$e->getMessage()}\n\n";
}

// Example 2: Logo from SVG file with custom positioning
echo "Example 2: Logo from SVG File with Custom Settings\n";
echo "---------------------------------------------------\n";

try {
    // Use the SVG logo file from the examples/logo/ directory
    $logoPath = __DIR__ . '/logo/logo-qrcode.svg';

    // Generate QR code with higher error correction for larger logo
    $qr = QrCode::encodeText('https://example.com/custom-logo', Ecc::HIGH);

    // Create logo configuration with custom settings
    $logoConfig = new LogoConfig(
        logoSource: $logoPath,
        sizePercentage: 0.25, // 25% - possible with HIGH error correction
        addBackground: true,
        backgroundColor: '#F8F9FA',
        backgroundPadding: 3,
        offsetX: 0.0,  // Centered
        offsetY: 0.0   // Centered
    );

    // Create QR code with logo
    $qrWithLogo = new QrCodeLogo($qr, $logoConfig);

    // Generate SVG with custom colors
    $svgOutput = $qrWithLogo->toSvg(
        border: 6,
        lightColor: '#F8F9FA',
        darkColor: '#212529'
    );
    file_put_contents(__DIR__ . '/output_logo_custom.svg', $svgOutput);

    echo "✓ Generated: output_logo_custom.svg\n";
    echo "  QR Code: {$qr->size}×{$qr->size} modules\n";
    echo "  Error Correction: {$qr->errorCorrectionLevel->name}\n";
    echo "  Logo Size: 25% of QR code\n";
    echo "  Logo Source: SVG file from examples/logo/ directory\n";
    echo "  Background: Light gray with padding\n\n";
} catch (LogoException $e) {
    echo "✗ Error: {$e->getMessage()}\n\n";
}

// Example 3: Multiple QR codes with different logo sizes
echo "Example 3: Different Logo Sizes by Error Correction Level\n";
echo "---------------------------------------------------------\n";

$testData = 'https://example.com/ecc-test';
$eccLevels = [
    ['level' => Ecc::LOW, 'name' => 'LOW', 'maxSize' => 0.15],
    ['level' => Ecc::MEDIUM, 'name' => 'MEDIUM', 'maxSize' => 0.20],
    ['level' => Ecc::QUARTILE, 'name' => 'QUARTILE', 'maxSize' => 0.25],
    ['level' => Ecc::HIGH, 'name' => 'HIGH', 'maxSize' => 0.30],
];

foreach ($eccLevels as $eccInfo) {
    try {
        $qr = QrCode::encodeText($testData, $eccInfo['level']);

        // Create a distinctive logo for each level
        $logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
            <rect x="5" y="5" width="90" height="90" fill="#6c757d" rx="15"/>
            <text x="50" y="40" text-anchor="middle" fill="white" font-family="Arial" font-size="12" font-weight="bold">' . $eccInfo['name'] . '</text>
            <text x="50" y="65" text-anchor="middle" fill="white" font-family="Arial" font-size="10">' . round($eccInfo['maxSize'] * 100) . '%</text>
        </svg>';

        $logoConfig = new LogoConfig(
            logoSource: $logoSvg,
            sizePercentage: $eccInfo['maxSize'],
            addBackground: true,
            backgroundColor: '#FFFFFF'
        );

        $qrWithLogo = new QrCodeLogo($qr, $logoConfig);
        $svgOutput = $qrWithLogo->toSvg();

        $filename = "output_logo_ecc_{$eccInfo['name']}.svg";
        file_put_contents(__DIR__ . '/' . strtolower($filename), $svgOutput);

        echo '✓ Generated: ' . strtolower($filename) . "\n";
        echo "  Error Correction: {$eccInfo['name']} (~" . round($eccInfo['maxSize'] * 100) . "% logo)\n";
    } catch (LogoException $e) {
        echo "✗ Error for {$eccInfo['name']}: {$e->getMessage()}\n";
    }
}
echo "\n";

// Example 4: Error handling demonstration
echo "Example 4: Error Handling Examples\n";
echo "----------------------------------\n";

// Test 1: Logo too large for error correction level
echo "Test 1: Logo size constraint violation\n";
try {
    $qr = QrCode::encodeText('Test data', Ecc::LOW);
    $logoConfig = new LogoConfig(
        logoSource: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="red"/></svg>',
        sizePercentage: 0.30 // Too large for LOW error correction
    );
    $qrWithLogo = new QrCodeLogo($qr, $logoConfig);
    echo "✗ Should have failed but didn't\n";
} catch (LogoException $e) {
    echo "✓ Correctly caught error: {$e->getMessage()}\n";
}

// Test 2: Invalid SVG content
echo "Test 2: Invalid SVG content\n";
try {
    $logoConfig = new LogoConfig(
        logoSource: '<div>Not an SVG</div>',
        sizePercentage: 0.15
    );
    $logoConfig->getLogoSvgContent();
    echo "✗ Should have failed but didn't\n";
} catch (LogoException $e) {
    echo "✓ Correctly caught error: {$e->getMessage()}\n";
}

// Test 3: Invalid configuration parameters
echo "Test 3: Invalid configuration parameters\n";
try {
    $logoConfig = new LogoConfig(
        logoSource: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="blue"/></svg>',
        sizePercentage: 1.5 // Invalid: > 1.0
    );
    echo "✗ Should have failed but didn't\n";
} catch (LogoException $e) {
    echo "✓ Correctly caught error: {$e->getMessage()}\n";
}

echo "\n";

// Example 5: PNG and JPEG output (if GD is available)
echo "Example 5: Raster Format Output\n";
echo "-------------------------------\n";

if (extension_loaded('gd')) {
    try {
        $qr = QrCode::encodeText('https://example.com/raster-test', Ecc::MEDIUM);

        $logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
            <rect x="10" y="10" width="80" height="80" fill="#dc3545" rx="10"/>
            <text x="50" y="55" text-anchor="middle" fill="white" font-family="Arial" font-size="14" font-weight="bold">PNG</text>
        </svg>';

        $logoConfig = new LogoConfig(
            logoSource: $logoSvg,
            sizePercentage: 0.18
        );

        $qrWithLogo = new QrCodeLogo($qr, $logoConfig);

        // Generate PNG
        $pngData = $qrWithLogo->toPng(scale: 8, border: 4);
        file_put_contents(__DIR__ . '/output_logo_test.png', $pngData);
        echo "✓ Generated: output_logo_test.png\n";

        // Generate JPEG
        $jpegData = $qrWithLogo->toJpeg(scale: 8, border: 4, quality: 95);
        file_put_contents(__DIR__ . '/output_logo_test.jpg', $jpegData);
        echo "✓ Generated: output_logo_test.jpg\n";

        echo "  Scale: 8 pixels per module\n";
        echo "  Border: 4 modules\n";
        echo "  JPEG Quality: 95%\n\n";
    } catch (LogoException $e) {
        echo "✗ Error generating raster formats: {$e->getMessage()}\n\n";
    }
} else {
    echo "⚠ GD extension not available - PNG/JPEG output skipped\n\n";
}

// Example 6: Advanced configuration showcase
echo "Example 6: Advanced Configuration Options\n";
echo "----------------------------------------\n";

try {
    $qr = QrCode::encodeText('https://example.com/advanced-config', Ecc::QUARTILE);

    $logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
        <defs>
            <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#6f42c1;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#e83e8c;stop-opacity:1" />
            </linearGradient>
        </defs>
        <circle cx="50" cy="50" r="45" fill="url(#grad)"/>
        <text x="50" y="35" text-anchor="middle" fill="white" font-family="Arial" font-size="10" font-weight="bold">ADVANCED</text>
        <text x="50" y="50" text-anchor="middle" fill="white" font-family="Arial" font-size="8">CONFIG</text>
        <text x="50" y="65" text-anchor="middle" fill="white" font-family="Arial" font-size="6">DEMO</text>
    </svg>';

    $logoConfig = new LogoConfig(
        logoSource: $logoSvg,
        sizePercentage: 0.22,
        addBackground: true,
        backgroundColor: '#F8F9FA',
        backgroundPadding: 4,
        offsetX: 0.1,  // Slightly offset to the right
        offsetY: -0.05, // Slightly offset upward
        maintainAspectRatio: true
    );

    $qrWithLogo = new QrCodeLogo($qr, $logoConfig);
    $svgOutput = $qrWithLogo->toSvg(
        border: 8,
        lightColor: '#FFFFFF',
        darkColor: '#495057'
    );

    file_put_contents(__DIR__ . '/output_logo_advanced.svg', $svgOutput);

    echo "✓ Generated: output_logo_advanced.svg\n";
    echo "  Features: Gradient logo, custom positioning, large border\n";
    echo "  Logo Size: 22% with 4-module background padding\n";
    echo "  Position: Slightly offset (right +10%, up -5%)\n\n";
} catch (LogoException $e) {
    echo "✗ Error: {$e->getMessage()}\n\n";
}

echo "All examples completed!\n";
echo "Check the generated files in the examples/ directory.\n";
echo "\nNote: The logo embedding feature maintains QR code readability\n";
echo "by respecting error correction levels and quiet zone requirements.\n";
