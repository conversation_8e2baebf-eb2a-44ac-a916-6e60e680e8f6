# Development Tools

This directory contains development and validation tools for the QR Code Generator library.

## Available Tools

### Migration Validation (`migration_validation.php`)

Comprehensive test to validate that the migration from the old structure to the new structure is working correctly. Tests both backward compatibility and new structure functionality.

**Usage:**

```bash
php tools/migration_validation.php
```

**What it tests:**

-   Old structure still works (backward compatibility)
-   New structure works correctly
-   Logo functionality in both structures
-   Utility functions in both namespaces
-   Enum compatibility between structures
-   Cross-namespace compatibility
-   Autoloading functionality

### Structure Validation (`structure_validation.php`)

Tests the new structure with autoloading to ensure all core classes are accessible in their new locations.

**Usage:**

```bash
php tools/structure_validation.php
```

**What it tests:**

-   Core enums (Ecc, Mode) from new locations
-   QrSegment from new location
-   QrCode from new location
-   Utility functions from new namespace
-   Autoloading functionality

### SVG Debug Validation (`debug_svg_validation.php`)

Debug tool for testing SVG validation functionality, particularly useful for testing logo SVG validation.

**Usage:**

```bash
php tools/debug_svg_validation.php
```

**What it tests:**

-   Invalid SVG rejection
-   Dangerous SVG (with scripts) rejection
-   Valid SVG acceptance
-   LogoException handling

## When to Use These Tools

-   **During development**: Run these tools to ensure changes don't break compatibility
-   **After refactoring**: Validate that the structure changes work correctly
-   **Before releases**: Ensure backward compatibility is maintained
-   **Debugging**: Use the SVG debug tool when troubleshooting logo-related issues

## Integration with CI/CD

These tools can be integrated into your CI/CD pipeline to automatically validate the library structure and compatibility:

```bash
# Run all validation tools
php tools/migration_validation.php
php tools/structure_validation.php
php tools/debug_svg_validation.php
```

All tools exit with code 0 on success and non-zero on failure, making them suitable for automated testing.
