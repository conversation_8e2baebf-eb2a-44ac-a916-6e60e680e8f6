# Migration Guide: Adding Logo Functionality

This guide helps you migrate from basic QR code generation to include logo embedding functionality.

## Overview

The logo embedding feature is designed to be **completely backward compatible**. Your existing QR code generation code will continue to work unchanged, and you can add logo functionality incrementally.

## Migration Steps

### Step 1: Update Dependencies

If using Composer:

```bash
composer update qrcodegen/qrcodegen-php
```

If using manual installation, add the new files:

```php
// Add these new includes after your existing ones
require_once 'src/Exceptions/LogoException.php';
require_once 'src/LogoConfig.php';
require_once 'src/QrCodeLogo.php';
require_once 'src/SvgRenderer.php';
```

### Step 2: Update Use Statements

Add the new classes to your use statements:

```php
<?php

// Existing imports (keep these)
use QrCodeGen\QrCode;
use QrCodeGen\Ecc;
use QrCodeGen\QrSegment;

// New imports for logo functionality
use QrCodeGen\QrCodeLogo;
use QrCodeGen\LogoConfig;
use QrCodeGen\SvgRenderer;
use QrCodeGen\Exceptions\LogoException;
```

### Step 3: Gradual Feature Adoption

#### Option A: Minimal Changes (Wrapper Approach)

Keep your existing code and add a wrapper function:

```php
// Your existing function
function generateQrCode(string $data, Ecc $ecc = Ecc::MEDIUM): QrCode
{
    return QrCode::encodeText($data, $ecc);
}

// New function with optional logo
function generateQrCodeWithLogo(
    string $data,
    ?string $logoSvg = null,
    Ecc $ecc = Ecc::MEDIUM,
    float $logoSize = 0.15
): QrCodeLogo|QrCode {
    $qr = QrCode::encodeText($data, $ecc);

    if ($logoSvg === null) {
        return $qr; // Return basic QR code
    }

    $config = new LogoConfig($logoSvg, $logoSize);
    return new QrCodeLogo($qr, $config);
}
```

#### Option B: Enhanced Existing Functions

Modify your existing functions to support logos:

```php
// Before
function createQrCode(string $data): string
{
    $qr = QrCode::encodeText($data, Ecc::MEDIUM);
    return $this->renderSvg($qr);
}

// After
function createQrCode(string $data, ?array $logoConfig = null): string
{
    $qr = QrCode::encodeText($data, Ecc::MEDIUM);

    if ($logoConfig !== null) {
        $config = new LogoConfig(
            logoSource: $logoConfig['source'],
            sizePercentage: $logoConfig['size'] ?? 0.15,
            addBackground: $logoConfig['background'] ?? true
        );
        $qrWithLogo = new QrCodeLogo($qr, $config);
        return $qrWithLogo->toSvg();
    }

    return $this->renderSvg($qr);
}
```

## Common Migration Patterns

### Pattern 1: Configuration-Based Approach

```php
class QrCodeGenerator
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'ecc' => Ecc::MEDIUM,
            'logo' => null,
            'logoSize' => 0.15,
            'logoBackground' => true,
        ], $config);
    }

    public function generate(string $data): string
    {
        $qr = QrCode::encodeText($data, $this->config['ecc']);

        if ($this->config['logo']) {
            $logoConfig = new LogoConfig(
                logoSource: $this->config['logo'],
                sizePercentage: $this->config['logoSize'],
                addBackground: $this->config['logoBackground']
            );
            $qrWithLogo = new QrCodeLogo($qr, $logoConfig);
            return $qrWithLogo->toSvg();
        }

        // Fallback to basic rendering
        return $this->renderBasicSvg($qr);
    }
}
```

### Pattern 2: Builder Pattern

```php
class QrCodeBuilder
{
    private string $data;
    private Ecc $ecc = Ecc::MEDIUM;
    private ?LogoConfig $logoConfig = null;

    public function data(string $data): self
    {
        $this->data = $data;
        return $this;
    }

    public function errorCorrection(Ecc $ecc): self
    {
        $this->ecc = $ecc;
        return $this;
    }

    public function logo(string $logoSvg, float $size = 0.15): self
    {
        $this->logoConfig = new LogoConfig($logoSvg, $size);
        return $this;
    }

    public function build(): QrCode|QrCodeLogo
    {
        $qr = QrCode::encodeText($this->data, $this->ecc);

        if ($this->logoConfig) {
            return new QrCodeLogo($qr, $this->logoConfig);
        }

        return $qr;
    }
}

// Usage
$qrCode = (new QrCodeBuilder())
    ->data("https://example.com")
    ->errorCorrection(Ecc::HIGH)
    ->logo($logoSvg, 0.20)
    ->build();
```

### Pattern 3: Factory Pattern

```php
class QrCodeFactory
{
    public static function create(string $data, array $options = []): QrCode|QrCodeLogo
    {
        $ecc = $options['ecc'] ?? Ecc::MEDIUM;
        $qr = QrCode::encodeText($data, $ecc);

        if (isset($options['logo'])) {
            $logoConfig = new LogoConfig(
                logoSource: $options['logo']['source'],
                sizePercentage: $options['logo']['size'] ?? 0.15,
                addBackground: $options['logo']['background'] ?? true,
                backgroundColor: $options['logo']['backgroundColor'] ?? '#FFFFFF'
            );
            return new QrCodeLogo($qr, $logoConfig);
        }

        return $qr;
    }
}

// Usage
$basicQr = QrCodeFactory::create("Hello World");
$logoQr = QrCodeFactory::create("Hello World", [
    'ecc' => Ecc::HIGH,
    'logo' => [
        'source' => $logoSvg,
        'size' => 0.20,
        'background' => true
    ]
]);
```

## Error Handling Migration

### Before (Basic Error Handling)

```php
try {
    $qr = QrCode::encodeText($data, $ecc);
    $svg = $this->renderSvg($qr);
} catch (Exception $e) {
    // Handle general errors
    return $this->handleError($e);
}
```

### After (Enhanced Error Handling)

```php
try {
    $qr = QrCode::encodeText($data, $ecc);

    if ($logoSvg) {
        $config = new LogoConfig($logoSvg, $logoSize);
        $qrWithLogo = new QrCodeLogo($qr, $config);
        $svg = $qrWithLogo->toSvg();
    } else {
        $svg = $this->renderSvg($qr);
    }
} catch (LogoException $e) {
    // Handle logo-specific errors
    return $this->handleLogoError($e);
} catch (Exception $e) {
    // Handle general errors
    return $this->handleError($e);
}
```

## Testing Migration

### Update Your Tests

```php
// Before
public function testQrCodeGeneration(): void
{
    $qr = QrCode::encodeText("Test", Ecc::MEDIUM);
    $this->assertInstanceOf(QrCode::class, $qr);
}

// After (backward compatibility test)
public function testQrCodeGeneration(): void
{
    $qr = QrCode::encodeText("Test", Ecc::MEDIUM);
    $this->assertInstanceOf(QrCode::class, $qr);
}

// New test for logo functionality
public function testQrCodeWithLogo(): void
{
    $qr = QrCode::encodeText("Test", Ecc::MEDIUM);
    $config = new LogoConfig($this->validSvg, 0.15);
    $qrWithLogo = new QrCodeLogo($qr, $config);

    $this->assertInstanceOf(QrCodeLogo::class, $qrWithLogo);
    $svg = $qrWithLogo->toSvg();
    $this->assertStringContainsString('<svg', $svg);
}
```

## Performance Considerations

### Memory Usage

Logo functionality adds minimal memory overhead:

-   LogoConfig: ~1KB per instance
-   QrCodeLogo: ~2KB per instance
-   SVG processing: Depends on logo complexity

### Processing Time

-   Basic QR codes: No performance impact
-   QR codes with logos: +10-50ms depending on logo complexity
-   Raster output: Additional time for format conversion

### Optimization Tips

```php
// Cache logo configurations for repeated use
class LogoCache
{
    private static array $cache = [];

    public static function getConfig(string $logoPath, float $size): LogoConfig
    {
        $key = md5($logoPath . $size);

        if (!isset(self::$cache[$key])) {
            self::$cache[$key] = new LogoConfig($logoPath, $size);
        }

        return self::$cache[$key];
    }
}
```

## Deployment Considerations

### Environment Requirements

-   PHP 8.2+ (same as before)
-   GD extension (optional, for PNG/JPEG output)
-   File system access (for file-based logos)
-   Network access (for URL-based logos)

### Configuration Updates

```php
// config/qr.php
return [
    'default_ecc' => 'MEDIUM',
    'logo' => [
        'enabled' => env('QR_LOGO_ENABLED', false),
        'default_size' => 0.15,
        'max_size' => 0.30,
        'allowed_sources' => ['file', 'url', 'string'],
        'cache_enabled' => true,
    ],
];
```

### Security Considerations

-   SVG validation is automatic
-   File path validation for logo sources
-   URL whitelist for remote logos
-   Size limits to prevent abuse

## Rollback Plan

If you need to rollback:

1. **Remove new includes**:

    ```php
    // Comment out or remove these lines
    // require_once 'src/Exceptions/LogoException.php';
    // require_once 'src/LogoConfig.php';
    // require_once 'src/QrCodeLogo.php';
    // require_once 'src/SvgRenderer.php';
    ```

2. **Disable logo features**:

    ```php
    // Add feature flag
    if (defined('ENABLE_LOGO_FEATURE') && ENABLE_LOGO_FEATURE) {
        // Logo code here
    } else {
        // Basic QR code generation
    }
    ```

3. **Fallback to basic rendering**:
    ```php
    try {
        // Try logo functionality
        $qrWithLogo = new QrCodeLogo($qr, $config);
        return $qrWithLogo->toSvg();
    } catch (Error $e) {
        // Fallback to basic rendering
        return $this->renderBasicSvg($qr);
    }
    ```

## Support and Troubleshooting

### Common Issues

1. **"Class not found" errors**: Check include paths
2. **"Logo too large" errors**: Adjust size or ECC level
3. **"Invalid SVG" errors**: Validate SVG content
4. **Performance issues**: Consider caching and optimization

### Getting Help

-   Check the detailed documentation for logo features
-   Review testing procedures in the testing guide
-   Run the examples in `examples/logo_examples.php`
-   Use validation tools in the `tools/` directory

### Best Practices

1. **Start small**: Begin with simple logos and basic configurations
2. **Test thoroughly**: Verify QR code readability with actual scanners
3. **Monitor performance**: Track generation times and memory usage
4. **Plan for errors**: Implement proper error handling and fallbacks
5. **Document changes**: Update your documentation and team knowledge

## Conclusion

The logo embedding feature is designed to integrate seamlessly with existing QR code generation workflows. By following this migration guide, you can add logo functionality incrementally while maintaining backward compatibility and system stability.
