<?php

declare(strict_types=1);

/**
 * Debug SVG validation functionality
 * 
 * Usage: php tools/debug_svg_validation.php
 */

// Manual includes (adjust paths for tools directory)
require_once __DIR__ . '/../src/functions.php';
require_once __DIR__ . '/../src/Ecc.php';
require_once __DIR__ . '/../src/Mode.php';
require_once __DIR__ . '/../src/QrSegment.php';
require_once __DIR__ . '/../src/QrCode.php';
require_once __DIR__ . '/../src/Exceptions/LogoException.php';
require_once __DIR__ . '/../src/LogoConfig.php';

use QrCodeGen\LogoConfig;
use QrCodeGen\Exceptions\LogoException;

echo "Debug SVG Validation\n";
echo "===================\n\n";

// Test invalid SVG
echo "Test 1: Invalid SVG\n";
try {
    $config = new LogoConfig('<div>Not SVG</div>');
    $config->getLogoSvgContent();
    echo "✗ Should have rejected invalid SVG\n";
} catch (LogoException $e) {
    echo "✓ Caught LogoException\n";
    echo "  Code: {$e->getCode()}\n";
    echo "  Expected: " . LogoException::INVALID_SVG . "\n";
    echo "  Message: {$e->getMessage()}\n";
} catch (Exception $e) {
    echo "✗ Caught unexpected exception: " . get_class($e) . "\n";
    echo "  Message: {$e->getMessage()}\n";
}

echo "\n";

// Test dangerous SVG
echo "Test 2: Dangerous SVG\n";
try {
    $config = new LogoConfig('<svg><script>alert("xss")</script></svg>');
    $config->getLogoSvgContent();
    echo "✗ Should have rejected dangerous SVG\n";
} catch (LogoException $e) {
    echo "✓ Caught LogoException\n";
    echo "  Code: {$e->getCode()}\n";
    echo "  Expected: " . LogoException::INVALID_SVG . "\n";
    echo "  Message: {$e->getMessage()}\n";
} catch (Exception $e) {
    echo "✗ Caught unexpected exception: " . get_class($e) . "\n";
    echo "  Message: {$e->getMessage()}\n";
}

echo "\n";

// Test valid SVG
echo "Test 3: Valid SVG\n";
try {
    $validSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="blue"/></svg>';
    $config = new LogoConfig($validSvg);
    $content = $config->getLogoSvgContent();
    echo "✓ Valid SVG accepted\n";
    echo "  Content length: " . strlen($content) . "\n";
} catch (Exception $e) {
    echo "✗ Valid SVG rejected: {$e->getMessage()}\n";
}
